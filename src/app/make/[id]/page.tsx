import DetailPage from '@/views/resume';
import type { Metadata } from 'next';

// 强制动态渲染，避免静态生成时的错误
export const dynamic = 'force-dynamic';

export const metadata: Metadata = {
  title: '在线简历制作工具-熊猫简历',
};

interface DetailProps {
  params: Promise<{
    id: string;
  }>;
  searchParams: Promise<{
    active?: string;
  }>;
}

export default async function Detail({ params, searchParams }: DetailProps) {
  // 等待params解析完成并获取id参数
  const { id } = await params;

  // 等待searchParams解析完成并获取active参数
  const { active } = await searchParams;

  // 将id参数和active参数传递给DetailPage组件
  return <DetailPage id={id} activeModule={active} />;
}
