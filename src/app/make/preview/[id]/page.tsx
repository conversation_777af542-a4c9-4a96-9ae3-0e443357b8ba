import PreviewPage from '@/views/preview';
import { getResumeDetail, serverUserApi } from '@/api/server';
import ErrorPage from '@/components/ErrorPage';

// 强制动态渲染，避免静态生成时的错误
export const dynamic = 'force-dynamic';

interface DetailProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function Preview({ params }: DetailProps) {
  // 等待params解析完成并获取id参数
  const { id } = await params;

  // 获取简历详情
  let resumeDetail = null;
  try {
    resumeDetail = await getResumeDetail(id);
  } catch (error) {
    // 返回错误页面
    return (
      <ErrorPage
        title="简历不存在"
        message="抱歉，您访问的简历不存在或已被删除，即将跳转到首页..."
        redirectDelay={1000}
        redirectTo="/"
      />
    );
  }

  // 获取用户信息
  let user = null;
  try {
    user = await serverUserApi.getGuestUserInfo();
  } catch (error) {
    // 出错时user保持为null
  }

  // 将resumeDetail和用户信息传递给PreviewPage组件
  return <PreviewPage resumeDetail={resumeDetail} initialUserInfo={user} />
}
