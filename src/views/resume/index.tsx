import DetailHeader from './components/DetailHeader';
import ContentArea from './components/ContentArea';
import CompletenessScore from './components/CompletenessScore';
import { serverUserApi, getResumeDetail } from '@/api/server';
import ErrorPage from '@/components/ErrorPage';

interface ResumePageProps {
  id: string;
  activeModule?: string;
}

export default async function ResumePage({ id, activeModule }: ResumePageProps) {
  // 简历ID已接收，可用于后续API调用

  // 获取用户信息
  let user = null;

  try {
    user = await serverUserApi.getGuestUserInfo();
  } catch (error) {
    // 出错时user保持为null
  }

  // 获取简历详情
  let resumeDetail = null;
  try {
    resumeDetail = await getResumeDetail(id);
  } catch (error) {
    // 返回错误页面
    return (
      <ErrorPage
        title="简历不存在"
        message="抱歉，您访问的简历不存在或已被删除，即将跳转到首页..."
        redirectDelay={1000}
        redirectTo="/"
      />
    );
  }

  return (
    <div className="bg-[#f7f7fc] min-h-screen flex flex-col relative">
      {/* 头部组件 - 全宽 */}
      <div className="bg-white border-b border-gray-100">
        <div className="container-custom">
          <DetailHeader onlineUsers={256} initialUserInfo={user} backUrl="/my" />
        </div>
      </div>

      {/* 内容区域组件 */}
      <ContentArea resumeDetail={resumeDetail} activeModule={activeModule} />

      {/* 简历完整度评分组件 - 悬浮在右侧 */}
      <CompletenessScore />
    </div>
  );
}
