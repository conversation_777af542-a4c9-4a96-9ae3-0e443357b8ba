import DetailHeader from './components/DetailHeader';
import ContentArea from './components/ContentArea';
import CompletenessScore from './components/CompletenessScore';
import { serverUserApi } from '@/api/server';

interface ResumePageProps {
  id: string;
  activeModule?: string;
}

export default async function ResumePage({ id, activeModule }: ResumePageProps) {
  // 获取用户信息
  let user = null;

  try {
    user = await serverUserApi.getGuestUserInfo();
  } catch (error) {
    // 出错时user保持为null
  }

  return (
    <div className="bg-[#f7f7fc] min-h-screen flex flex-col relative">
      {/* 头部组件 - 全宽 */}
      <div className="bg-white border-b border-gray-100">
        <div className="container-custom">
          <DetailHeader onlineUsers={256} initialUserInfo={user} backUrl="/my" />
        </div>
      </div>

      {/* 内容区域组件 */}
      <ContentArea resumeId={id} activeModule={activeModule} />

      {/* 简历完整度评分组件 - 悬浮在右侧 */}
      <CompletenessScore />
    </div>
  );
}
