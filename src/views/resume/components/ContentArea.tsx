'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { ResizablePanelGroup, ResizablePanel } from "@/components/ui/resizable";
import { CustomResizableHandle } from "@/components/CustomResizableHandle";
import ModuleManager from '@/components/ModuleManager';
import Toolbar from '@/components/Toolbar';
import GlobalLoading from '@/components/GlobalLoading';
import FullScreenLoading from '@/components/FullScreenLoading';
import ResumeArea from '@/components/ResumeArea';
import SmartOnePageRollback from '@/components/SmartOnePageRollback';
import { useResumeStyleInitializer } from "@/hooks/useResumeStyleInitializer";
import { useModuleStore } from '@/store/useModuleStore';
import { useSmartOnePageStore } from '@/store/useSmartOnePageStore';
import { ResumeDetailResponse } from '@/api/server/types/resume';
import { resumeApi } from '@/api/client';
import { toast } from 'sonner';

interface ContentAreaProps {
  resumeDetail?: ResumeDetailResponse;
  activeModule?: string;
}


/**
 * 内容区域组件
 * 包含左侧模块管理和右侧简历预览
 */
export default function ContentArea({ resumeDetail, activeModule }: ContentAreaProps) {
  // 使用Hook初始化简历样式
  useResumeStyleInitializer();

  // 获取 store 中的初始化方法和相关状态
  const initializeFromResumeDetail = useModuleStore(state => state.initializeFromResumeDetail);
  const isInitializing = useModuleStore(state => state.isInitializing);
  const modules = useModuleStore(state => state.modules);
  const setActiveModule = useModuleStore(state => state.setActiveModule);

  // 智能一页状态
  const isSmartOnePageProcessing = useSmartOnePageStore(state => state.isProcessing);

  // 客户端刷新简历详情相关状态
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [refreshError, setRefreshError] = useState<string | null>(null);

  const toolbarRef = useRef<HTMLDivElement>(null);

  // 判断是否需要显示 loading：正在初始化 或者 还没有模块数据
  const shouldShowLoading = isInitializing || Object.keys(modules).length === 0;

  /**
   * 客户端获取简历详情
   * 调用客户端API重新获取简历数据并更新store
   */
  const refreshResumeDetail = useCallback(async () => {
    if (!resumeDetail?.id) {
      toast.error('简历ID不存在，无法刷新数据');
      return;
    }

    setIsRefreshing(true);
    setRefreshError(null);

    try {
      // 调用客户端API获取简历详情
      const newResumeDetail = await resumeApi.getResumeDetail(resumeDetail.id.toString());

      // 使用新数据初始化store
      if (initializeFromResumeDetail) {
        initializeFromResumeDetail(newResumeDetail);
      }

      toast.success('简历数据已刷新', {
        position: 'top-center'
      });
    } catch (error) {
      console.error('刷新简历详情失败:', error);
      const errorMessage = error instanceof Error ? error.message : '刷新简历数据失败，请稍后重试';
      setRefreshError(errorMessage);
      toast.error(errorMessage, {
        position: 'top-center'
      });
    } finally {
      setIsRefreshing(false);
    }
  }, [resumeDetail?.id, initializeFromResumeDetail]);

  // 当 resumeDetail 存在时，初始化 store 数据
  useEffect(() => {
    if (resumeDetail && initializeFromResumeDetail) {
      initializeFromResumeDetail(resumeDetail);
    }
  }, [resumeDetail, initializeFromResumeDetail]);

  // 处理URL中的activeModule参数，在数据初始化完成后设置活跃模块
  useEffect(() => {
    if (activeModule && !isInitializing && setActiveModule) {
      // 延迟一点时间确保模块数据已经完全加载
      const timer = setTimeout(() => {
        setActiveModule(activeModule);
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [activeModule, isInitializing, setActiveModule]);



  return (
    <>
      {/* 全局 Loading 效果 */}
      <GlobalLoading
        isLoading={shouldShowLoading}
        message="正在初始化简历数据..."
      />

      {/* 智能一页 Loading 效果 */}
      <FullScreenLoading
        isVisible={isSmartOnePageProcessing}
        message="智能一页调整中..."
      />

      {/* 智能一页回滚按钮 */}
      <SmartOnePageRollback />

      <div className="flex-1">
        <div className="container-custom">
          <ResizablePanelGroup
            direction="horizontal"
            className="min-h-[calc(100vh-56px-2rem)]"
          >
            {/* 左侧模块管理 - 占据剩余宽度 */}
            <ResizablePanel defaultSize={54} minSize={30}>
              <div className="h-full w-full flex justify-center">
                <div className="w-full" style={{ maxWidth: '100%' }}>
                  <ModuleManager className="h-full sticky top-8" />
                </div>
              </div>
            </ResizablePanel>

            {/* 可调整大小的分隔线 */}
            <CustomResizableHandle className='w-3 bg-white hover:bg-gray-400 transition-colors duration-200' />

            {/* 右侧区域 - 动态宽度 */}
            <ResizablePanel defaultSize={46} minSize={30}>
              <div className="h-full w-full flex flex-col items-center justify-start py-4 shadow-md rounded-md "  style={{ backgroundColor: '#f1f1fb' }}>
                {/* 容器 - 占满父级容器宽度 */}
                <div className="flex flex-col justify-start items-center w-full px-4">
                  <div ref={toolbarRef} className="w-full mb-4">
                    <div className="flex items-center gap-2">
                      <div className="flex-1">
                        <Toolbar className="rounded-lg" />
                      </div>
                      {/* 刷新简历数据按钮 */}
                      <button
                        onClick={refreshResumeDetail}
                        disabled={isRefreshing || shouldShowLoading}
                        className="px-3 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white rounded-lg transition-colors duration-200 flex items-center gap-1 text-sm"
                        title="重新获取简历数据"
                      >
                        {isRefreshing ? (
                          <>
                            <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            刷新中
                          </>
                        ) : (
                          <>
                            <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            刷新数据
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                  <div
                    className="w-full overflow-y-scroll hide-scrollbar"
                    style={{
                      height: 'calc(100vh - 56px - 2rem - 2rem - 44px - 1rem)' // 减去Toolbar高度(44px) + mb-4(16px)
                    }}
                  >
                    <ResumeArea containerRef={toolbarRef} />
                  </div>
                </div>
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        </div>
      </div>
    </>
  );
}
